{"name": "shadcn-admin", "private": false, "version": "1.4.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format:check": "prettier --check .", "format": "prettier --write .", "knip": "knip"}, "dependencies": {"@clerk/clerk-react": "^5.31.4", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@tabler/icons-react": "^3.31.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.3", "@tanstack/react-router": "^1.120.10", "@tanstack/react-table": "^8.21.3", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.488.0", "react": "^19.1.0", "react-day-picker": "9.6.6", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-top-loading-bar": "^3.0.2", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.24.0", "@faker-js/faker": "^9.7.0", "@tanstack/eslint-plugin-query": "^5.73.3", "@tanstack/react-query-devtools": "^5.74.3", "@tanstack/react-router-devtools": "^1.120.10", "@tanstack/router-plugin": "^1.120.10", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.24.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "knip": "^5.50.4", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.2.7"}}