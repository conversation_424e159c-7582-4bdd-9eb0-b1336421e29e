
# Module Leads (<PERSON><PERSON><PERSON><PERSON>)

<PERSON><PERSON>le Leads hỗ trợ quản lý khách hàng tiềm năng, đư<PERSON><PERSON> tối ưu cho quy trình B2C nhanh gọn và hỗ trợ B2B với các thông tin tùy chọn.

## Tính Năng <PERSON>ính

### Chế Độ Hiển Thị
- **Table View:** Hiển thị danh sách dưới dạng bảng.
- **Kanban Board:** Hiển thị dạng thẻ, dễ theo dõi trạng thái.

### Quản Lý Trạng Thái
- **Trạng Thái:**
  - Open (Mở)
  - Contacted (Đ<PERSON> liê<PERSON> hệ)
  - Qualified (Đủ điều kiện)
  - Unqualified (Không đủ điều kiện)
- **Chuyển Đổi Trạng Thái:** Thực hiện thủ công.

### Lọc và Tìm <PERSON>
- **L<PERSON><PERSON>:** <PERSON>, ng<PERSON><PERSON><PERSON> lead hoặc tiêu chí <PERSON> (Budget, Authority, Need, Timeline).
- **<PERSON><PERSON><PERSON>:** <PERSON><PERSON> dụng từ khóa.

### Tiêu Chí BANT (Chủ yếu cho B2B, Tùy chọn cho B2C)
- **Nhập Thủ Công:**
  - Ngân sách (Budget)
  - Quyền quyết định (Authority)
  - Nhu cầu (Need)
  - Thời gian (Timeline)

### Nhập Dữ Liệu
- Từ file Excel/CSV.
- Qua biểu mẫu web.
- Đồng bộ từ chiến dịch marketing.

### Chi Tiết Lead
- **Thông Tin Cơ Bản:**
  - Tên
  - Email
  - Số điện thoại
  - Công ty (tùy chọn)
- **Lịch Sử Hoạt Động:**
  - Ghi chú
  - Email
  - Cuộc gọi
  - Công việc (Tasks)
  - Cuộc họp
- **Tin Nhắn Đa Kênh:** Email, Messenger, Zalo.

## Màn Hình và Tính Năng Chi Tiết

### 1. Danh Sách Leads (List View)
- **Hiển Thị:** Dạng bảng (Table View) hoặc thẻ (Kanban Board).
- **Tính Năng:**
  - Lọc theo trạng thái, nguồn hoặc tiêu chí BANT.
  - Tìm kiếm nhanh.
  - Chuyển đổi giữa các chế độ hiển thị.
  - Xuất dữ liệu sang Excel.
- **Cải Thiện Giao Diện:**
  - **Vấn Đề Hiện Tại:** Phải cuộn ngang để xem cột cuối, gây bất tiện.
  - **Giải Pháp:**
    - Cố định cột đầu (như Tên, Email) để thông tin chính luôn hiển thị.
    - Tùy chỉnh cột: Chọn cột hiển thị (ví dụ: chỉ Tên, Email, Trạng thái).
    - Kéo thả cột: Sắp xếp thứ tự cột theo ý muốn.
    - Thanh cuộn thông minh: Thêm nút điều hướng nhanh (trái/phải).

### 2. Chi Tiết Lead (Detail View)
- **Hiển Thị:**
  - Thông tin liên hệ.
  - Lịch sử hoạt động (cuộc gọi, email, ghi chú, công việc).
  - Tin nhắn đa kênh (Email, Messenger, Zalo).
- **Tính Năng:**
  - Chỉnh sửa thông tin lead trực tiếp khi rê chuột vào trường (xóa nút 3 chấm).
  - Thêm ghi chú, công việc, email, cuộc gọi hoặc cuộc họp.
  - Gửi tin nhắn đa kênh.
- **Cải Thiện:**
  - Hiển thị biểu tượng bút chì khi rê chuột để chỉnh sửa nhanh.
  - Gợi ý hành động tiếp theo (ví dụ: "Gọi lại", "Gửi email follow-up") dựa trên trạng thái.

### 3. Nhập Leads (Import View)
- **Hiển Thị:** Giao diện nhập dữ liệu từ file hoặc biểu mẫu.
- **Tính Năng:**
  - Tải lên file Excel/CSV.
  - Ánh xạ trường dữ liệu.
  - Xem trước dữ liệu trước khi nhập.

## Tính Năng Calls (Cuộc Gọi)
Khi chọn "Calls", hiển thị pop-up **Note Call** với các trường:

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Subject                 | Mô tả ngắn mục đích cuộc gọi (VD: "Follow-up", "Giới thiệu sản phẩm").   |
| Call Start Time         | Thời điểm bắt đầu.                                                       |
| Call Duration           | Thời gian cuộc gọi (phút/giờ).                                           |
| Related To              | Liên kết với Lead, Contact hoặc đối tượng CRM.                           |
| Call Status             | Scheduled (Đã lên lịch), Held (Đã thực hiện), Not Held (Không thực hiện).|
| Call Type               | Inbound (Gọi đến), Outbound (Gọi đi).                                    |
| Description             | Ghi chú chi tiết nội dung, kết quả hoặc hành động tiếp theo.             |
| Assigned To             | Người hoặc nhóm thực hiện cuộc gọi.                                      |
| Call Result             | Kết quả cụ thể (VD: "Khách đồng ý gặp", "Không liên lạc được").          |
| Reminder                | Cài đặt thông báo trước cuộc gọi.                                        |
| Contact Name            | Tự động điền nếu đã liên kết với Contact.                                |
| Call Purpose            | Lý do cụ thể (VD: Sales, Support, Follow-up).                            |
| Attachments             | Đính kèm tài liệu, ghi âm hoặc file liên quan.                           |

## Module Notes (Ghi Chú)
Các trường thông tin:

| **Trường**              | **Mô Tả**                                              |
|--------------------------|-------------------------------------------------------|
| Title/Subject           | Tóm tắt nội dung ghi chú.                             |
| Note Content            | Chi tiết ghi chú.                                     |
| Related To              | Liên kết với Lead, Contact hoặc đối tượng khác.       |
| Created By              | Tên người tạo ghi chú.                                |
| Created Time            | Thời điểm tạo.                                        |
| Modified By             | Tên người chỉnh sửa gần nhất.                         |
| Modified Time           | Thời điểm chỉnh sửa gần nhất.                         |
| Attachments             | Đính kèm file liên quan.                              |
| Tags                    | Thêm thẻ để phân loại.                                |
| Assigned To             | Người hoặc nhóm được giao.                            |

## Thay Đổi Trong Module Leads
- **Xóa:** Tính năng Lịch hẹn.
- **Thay Đổi:** Xóa nút 3 chấm chỉnh sửa, thay bằng chỉnh sửa trực tiếp khi rê chuột vào trường thông tin.

---

# Module Tasks (Nhiệm Vụ)

Module Tasks hỗ trợ quản lý công việc liên quan đến khách hàng hoặc dự án, giúp phân công, theo dõi tiến độ và quản lý hiệu quả.

## Tính Năng Chính

### Chế Độ Hiển Thị
- **Table View:** Hiển thị danh sách nhiệm vụ dạng bảng.
- **Kanban Board:** Hiển thị dạng thẻ, dễ theo dõi trạng thái.
- **Gantt Chart:** Đã bỏ theo yêu cầu.

### Lọc và Tìm Kiếm
- **Lọc:** Theo trạng thái, mức ưu tiên, người được giao hoặc thời hạn.
- **Tìm Kiếm Nhanh:** Theo tên nhiệm vụ (hỗ trợ tìm gần đúng với từ khóa).

### Quản Lý Chi Tiết
- **Thông Tin:**
  - Tiêu đề
  - Mô tả
  - Trạng thái: To Do, In Progress, Done
  - Người tạo/thực hiện
  - Thời hạn
  - Ưu tiên: Cao, Trung, Thấp
- **Liên Kết:** Với Leads, Accounts, Deals.

## Màn Hình và Tính Năng Chi Tiết

### 1. Danh Sách Tasks (List View)
- **Hiển Thị:** Dạng bảng (Table View) hoặc thẻ (Kanban Board).
- **Tính Năng:**
  - Lọc theo trạng thái, ưu tiên, người được giao hoặc thời hạn.
  - Tìm kiếm nhanh theo tên nhiệm vụ.
  - Sắp xếp cột trong Table View.
  - Kéo thả trạng thái trong Kanban Board.
- **Cải Thiện Giao Diện:**
  - Cố định cột đầu (như Tiêu đề, Trạng thái) khi cuộn ngang.
  - Tùy chỉnh cột hiển thị (VD: chỉ Tiêu đề, Thời hạn, Người phụ trách).
  - Thêm nút điều hướng nhanh (trái/phải) trên thanh cuộn ngang.
  - Tích hợp biểu tượng trực quan (màu sắc, trạng thái) để nhận biết nhiệm vụ quan trọng.

### 2. Chi Tiết Task (Detail View)
- **Hiển Thị:**
  - Thông tin: Tiêu đề, mô tả, người thực hiện, thời hạn, đối tượng liên quan (Lead, Account, Deal).
  - Lịch sử hoạt động: Nhật ký cập nhật trạng thái, chỉnh sửa hoặc bình luận.
- **Tính Năng:**
  - Chỉnh sửa thông tin nhiệm vụ.
  - Thêm bình luận.
  - Cập nhật trạng thái.
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Subject                 | Mô tả ngắn gọn về nhiệm vụ.                                              |
| Status                  | Not Started, In Progress, Completed, Deferred.                           |
| Priority                | High (Cao), Medium (Trung), Low (Thấp).                                  |
| Due Date                | Ngày dự kiến hoàn thành.                                                 |
| Related To              | Liên kết với Lead, Contact, Account, Opportunity.                        |
| Assigned To             | Người hoặc nhóm được giao.                                               |
| Description             | Chi tiết nội dung nhiệm vụ.                                              |
| Start Date              | Thời điểm bắt đầu.                                                       |
| Reminder                | Cài đặt thông báo trước thời hạn.                                        |
| Contact Name            | Tự động điền nếu liên kết với Contact.                                   |
| Attachments             | Tài liệu hoặc file liên quan.                                            |
| Created By              | Tên người tạo nhiệm vụ.                                                  |
| Created Time            | Thời điểm tạo.                                                           |
| Modified By             | Tên người chỉnh sửa gần nhất.                                            |
| Modified Time           | Thời điểm chỉnh sửa gần nhất.                                            |
| Task Outcome            | Kết quả hoàn thành (VD: "Hoàn thành đúng hạn").                          |
| Tags                    | Phân loại để tìm kiếm hoặc nhóm nhiệm vụ.                                |
| Activity History        | Nhật ký các thay đổi hoặc hành động liên quan.                           |

- **Cải Thiện Giao Diện:**
  - Thêm chỉnh sửa nhanh bằng biểu tượng bút chì khi rê chuột vào trường thông tin.

### 3. Tạo Task (Create View)
- **Hiển Thị:** Giao diện nhập thông tin nhiệm vụ mới.
- **Tính Năng:**
  - Chọn người thực hiện.
  - Liên kết với Lead, Account, Deal.
  - Đặt ưu tiên, thời hạn hoặc nhiệm vụ định kỳ.
  - Nhân bản nhiệm vụ tương tự.
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Subject                 | Mô tả ngắn gọn về nhiệm vụ.                                              |
| Status                  | Not Started, In Progress, Completed, Deferred, Waiting on someone else.  |
| Priority                | High (Cao), Medium (Trung), Low (Thấp).                                  |
| Due Date                | Ngày dự kiến hoàn thành.                                                 |
| Related To              | Liên kết với Lead, Contact, Account, Opportunity.                        |
| Assigned To             | Người hoặc nhóm được giao.                                               |
| Description             | Chi tiết nội dung, các bước thực hiện.                                   |
| Start Date              | Thời điểm bắt đầu.                                                       |
| Reminder                | Cài đặt thông báo trước thời hạn.                                        |
| Contact Name            | Tự động điền nếu liên kết với Contact.                                   |
| Attachments             | Đính kèm tài liệu liên quan.                                             |
| Set Recurrence          | Cài đặt lặp lại theo ngày, tuần, tháng.                                  |
| Duplicate               | Tạo nhanh nhiệm vụ tương tự.                                             |

- **Cải Thiện Giao Diện:**
  - Thêm mẫu nhiệm vụ (VD: "Gọi khách hàng", "Follow-up deal") để nhập nhanh.

---

# Module Campaigns (Chiến Dịch Marketing)

Module Campaigns quản lý các chiến dịch marketing qua Email, tập trung vào B2C, giúp tạo, theo dõi và phân tích hiệu quả chiến dịch.

## Tính Năng Chính

### Quản Lý Kênh
- Hỗ trợ chiến dịch qua Email.

### Lọc và Tìm Kiếm
- **Lọc:** Theo trạng thái, loại chiến dịch, người tạo hoặc thời gian.
- **Tìm Kiếm Nhanh:** Bằng từ khóa.

### Phân Tích Hiệu Suất
- **Theo Dõi:**
  - Open rate (Tỷ lệ mở)
  - Click rate (Tỷ lệ nhấp)
  - Conversion rate (Tỷ lệ chuyển đổi từ lead sang deal)

### Theo Dõi Sự Kiện
- **Sự Kiện:**
  - Email sent (Đã gửi)
  - Opened (Đã mở)
  - Clicked (Đã nhấp)

### Chi Tiết Chiến Dịch
- **Thông Tin:**
  - Tên
  - Mục tiêu
  - Thời gian
  - Lịch gửi (chọn thủ công)

## Màn Hình và Tính Năng Chi Tiết

### 1. Danh Sách Campaigns (List View)
- **Hiển Thị:** Danh sách chiến dịch dạng bảng.
- **Tính Năng:**
  - Lọc theo trạng thái, loại chiến dịch hoặc thời gian.
  - Tìm kiếm nhanh.
  - Xem số liệu hiệu suất (open rate, click rate).
- **Cải Thiện Giao Diện:**
  - Cố định cột chính (như Tên chiến dịch, Trạng thái) khi cuộn ngang.
  - Tùy chỉnh cột hiển thị (VD: Tên, Trạng thái, Open rate).
  - Thanh cuộn thông minh: Thêm nút điều hướng nhanh (trái/phải).
  - Mã màu trạng thái (VD: Active - xanh, Completed - xám).

### 2. Chi Tiết Campaign (Detail View)
- **Hiển Thị:**
  - Thông tin: Mục tiêu, thời gian, biểu đồ hiệu suất (open rate, click rate, conversion rate).
  - Lịch sử hoạt động: Nhật ký cập nhật hoặc hành động liên quan.
- **Tính Năng:**
  - Xem chi tiết sự kiện (email sent, opened, clicked).
  - Chỉnh sửa thông tin chiến dịch.
  - Xuất báo cáo Excel.
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Campaign Name           | Tên hoặc tiêu đề (VD: "Chiến dịch Email Q4 2025").                       |
| Campaign Status         | Planning, Active, Inactive, Completed.                                   |
| Campaign Type           | Email, Webinar, Advertisement, Social Media, Event.                      |
| Start Date              | Ngày bắt đầu chiến dịch.                                                 |
| End Date                | Ngày kết thúc chiến dịch.                                                |
| Assigned To             | Người hoặc nhóm quản lý chiến dịch.                                      |
| Description             | Chi tiết về mục tiêu, nội dung hoặc phạm vi chiến dịch.                  |
| Budgeted Cost           | Số tiền dự kiến chi.                                                     |
| Actual Cost             | Số tiền đã chi.                                                          |
| Expected Revenue        | Doanh thu dự kiến.                                                       |
| Revenue Real            | Doanh thu thực tế đạt được.                                              |
| Target Audience         | Nhóm khách hàng (VD: Leads, Contacts, phân khúc cụ thể).                 |
| Target Size             | Số lượng Leads hoặc Contacts được nhắm đến.                              |
| Created By              | Tên người tạo chiến dịch.                                                |
| Created Time            | Thời điểm tạo chiến dịch.                                                |
| Modified By             | Tên người chỉnh sửa gần nhất.                                            |
| Modified Time           | Thời điểm chỉnh sửa gần nhất.                                            |
| Attachments             | Tài liệu liên quan (VD: nội dung email, banner).                         |
| Tags                    | Phân loại để tìm kiếm hoặc nhóm chiến dịch.                              |
| Campaign Objective      | Mục tiêu cụ thể (VD: Tăng nhận diện thương hiệu).                        |
| ROI                     | Lợi tức đầu tư (tính từ doanh thu và chi phí).                           |
| Activity History        | Nhật ký thay đổi hoặc hành động.                                         |
| Email Template          | Mẫu email dùng cho chiến dịch.                                           |
| Response Tracking       | Tỷ lệ mở, nhấp hoặc chuyển đổi.                                          |

- **Cải Thiện Giao Diện:**
  - Biểu đồ trực quan: Hiển thị open rate, click rate, conversion rate dạng cột/đường, hỗ trợ phóng to/thu nhỏ.
  - Chỉnh sửa nhanh: Biểu tượng bút chì khi rê chuột vào trường thông tin.
  - Dòng thời gian hoạt động: Hiển thị lịch sử dạng timeline.

### 3. Tạo Campaign (Create View)
- **Hiển Thị:** Giao diện thiết lập chiến dịch mới.
- **Tính Năng:**
  - Nhập nội dung email.
  - Chọn lịch gửi (thủ công).
  - Chọn đối tượng mục tiêu (Leads, Contacts, phân khúc).
- **Cải Thiện Giao Diện:**
  - Mẫu email sẵn có (VD: "Giới thiệu sản phẩm", "Khuyến mãi").
  - Xem trước email trước khi lưu.
  - Gợi ý đối tượng dựa trên lịch sử chiến dịch.

### 4. Phân Tích Campaign (Analytics View)
- **Lưu Ý:** Tính năng này được cân nhắc triển khai sau MVP.
- **Hiển Thị:** Biểu đồ về open rate, click rate, conversion rate.
- **Tính Năng:**
  - Xem chi tiết sự kiện (email sent, opened, clicked).
- **Cải Thiện Giao Diện:**
  - Thêm bộ lọc thời gian (ngày, tuần, tháng).
  - Hỗ trợ so sánh hiệu suất với chiến dịch trước.
  - Biểu đồ tương tác: Nhấp vào biểu đồ để xem chi tiết sự kiện.

---

# Module Orders (Đơn Hàng)

Module Orders quản lý đơn hàng, ưu tiên B2C với quy trình đơn giản, hỗ trợ thanh toán linh hoạt và hoàn tiền cho B2B.

## Tính Năng Chính

### Quản Lý Đơn Hàng
- **Thông Tin:**
  - Mã đơn
  - Ngày tạo
  - Trạng thái
  - Khách hàng
  - Sản phẩm
- **Trạng Thái:** Draft, In Progress, Confirmed, Cancelled.

### Quản Lý Thanh Toán
- **Trạng Thái Thanh Toán:** Đã thanh toán, Chưa thanh toán đủ, Đã hoàn tiền.
- **Phương Thức Thanh Toán:** Tiền mặt, Chuyển khoản.
- **Lịch Sử Thanh Toán:** Bao gồm giao dịch và hoàn tiền.

### Chi Tiết Đơn Hàng
- Lịch sử thay đổi, thông tin giao hàng, ghi chú.
- Xử lý thanh toán: Hỗ trợ thanh toán nhiều đợt, hoàn tiền, ghi chú nợ.

### Chỉnh Sửa Đơn Hàng
- Chỉnh sửa sản phẩm, số lượng, khách hàng khi trạng thái là Draft hoặc In Progress (không giới hạn).

## Màn Hình và Tính Năng Chi Tiết

### 1. Danh Sách Orders (List View)
- **Hiển Thị:** Danh sách đơn hàng dạng bảng.
- **Tính Năng:**
  - Lọc theo trạng thái hoặc trạng thái thanh toán.
  - Tìm kiếm nhanh theo mã đơn, khách hàng hoặc sản phẩm.
  - Xuất dữ liệu sang Excel.
- **Cải Thiện Giao Diện:**
  - Cố định cột chính (Mã đơn, Tên khách hàng, Trạng thái) khi cuộn ngang.
  - Tùy chỉnh cột hiển thị (VD: Mã đơn, Tổng giá trị, Trạng thái).
  - Thanh cuộn thông minh: Thêm nút điều hướng nhanh (trái/phải).
  - Mã màu trạng thái (VD: Confirmed - xanh, Cancelled - đỏ).

### 2. Chi Tiết Order (Detail View)
- **Hiển Thị:**
  - Thông tin: Khách hàng, sản phẩm, thanh toán (lịch sử thanh toán/hoàn tiền), giao hàng, ghi chú.
  - Lịch sử thay đổi: Nhật ký cập nhật trạng thái hoặc chỉnh sửa.
- **Tính Năng:**
  - Cập nhật trạng thái đơn hàng.
  - Chỉnh sửa đơn hàng (khi ở trạng thái Draft hoặc In Progress).
  - Thêm ghi chú.
  - Nhập thanh toán hoặc hoàn tiền.
  - Xem thông tin nợ.
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Order Number            | Mã định danh duy nhất, tự động sinh.                                     |
| Subject                 | Mô tả ngắn gọn (VD: "Đơn hàng #123 cho khách hàng ABC").                  |
| Contact Name            | Người liên hệ chính (liên kết với module Contacts).                      |
| Order Status            | Draft, Confirmed, In Progress, Cancelled.                                |
| Order Date              | Ngày đơn hàng được tạo.                                                  |
| Due Date                | Ngày dự kiến giao hàng hoặc hoàn thành.                                  |
| Assigned To             | Người hoặc nhóm quản lý đơn hàng.                                        |
| Total Amount            | Giá trị đơn hàng, bao gồm thuế và phí.                                   |
| Subtotal                | Giá trị trước thuế hoặc chiết khấu.                                      |
| Tax                     | Số tiền thuế áp dụng.                                                    |
| Discount                | Số tiền chiết khấu (nếu có).                                             |
| Product Details         | Tên sản phẩm, Số lượng, Đơn giá, Tổng giá sản phẩm.                      |
| Related To              | Liên kết với module Deal.                                                |
| Description             | Ghi chú hoặc chi tiết bổ sung.                                           |

- **Cải Thiện Giao Diện:**
  - Chỉnh sửa nhanh: Biểu tượng bút chì khi rê chuột vào trường thông tin.
  - Dòng thời gian thanh toán: Hiển thị lịch sử thanh toán dạng timeline.
  - Cảnh báo nợ: Làm nổi bật đơn hàng chưa thanh toán đủ bằng màu sắc/biểu tượng.

### 3. Tạo Order (Create View)
- **Hiển Thị:** Giao diện nhập thông tin đơn hàng mới.
- **Tính Năng:**
  - Chọn khách hàng từ module Contacts.
  - Thêm sản phẩm (tên, số lượng, đơn giá).
  - Nhập thông tin thanh toán (phương thức, trạng thái).
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Subject                 | Mô tả ngắn gọn (VD: "Đơn hàng #123 cho khách hàng ABC").                  |
| Contact Name            | Người liên hệ chính (liên kết với module Contacts).                      |
| Order Status            | Draft, Confirmed, In Progress (mặc định: Draft).                         |
| Order Date              | Mặc định là ngày hiện tại.                                               |
| Due Date                | Ngày dự kiến giao hàng hoặc hoàn thành.                                  |
| Assigned To             | Người hoặc nhóm quản lý đơn hàng.                                        |
| Product Details         | Tên sản phẩm, Số lượng, Đơn giá, Tổng giá sản phẩm.                      |
| Subtotal                | Giá trị trước thuế hoặc chiết khấu.                                      |
| Tax                     | Số tiền thuế áp dụng.                                                    |
| Discount                | Số tiền chiết khấu (nếu có).                                             |
| Total Amount            | Giá trị đơn hàng, bao gồm thuế và phí.                                   |
| Related To              | Liên kết với module Deal.                                                |
| Description             | Ghi chú hoặc chi tiết bổ sung.                                           |

- **Cải Thiện Giao Diện:**
  - Gợi ý sản phẩm: Hiển thị danh sách sản phẩm khi nhập tên, kèm giá và thông tin cơ bản.
  - Tính toán tự động: Cập nhật Subtotal, Tax, Total Amount ngay khi chỉnh sửa.
  - Mẫu đơn hàng: Cung cấp mẫu phổ biến (VD: "Đơn hàng B2C tiêu chuẩn").
  - Xem trước liên kết: Hiển thị thông tin cơ bản của Deal khi chọn Related To.

## Giải Pháp Thanh Toán
- **Quản Lý Thanh Toán Nhiều Đợt:** Cho phép nhập các lần thanh toán riêng lẻ, gắn với trạng thái (Đã thanh toán, Chưa thanh toán đủ, Đã hoàn tiền).
- **Trạng Thái Thanh Toán:**
  - Tự động cập nhật dựa trên tổng số tiền đã thanh toán so với Total Amount.
  - Hiển thị cảnh báo khi đơn hàng chưa thanh toán đủ.
- **Hoàn Tiền:** Ghi nhận giao dịch hoàn tiền trong lịch sử thanh toán, bao gồm số tiền.

---

# Module Products (Sản Phẩm)

Module Products quản lý danh mục sản phẩm/dịch vụ, hỗ trợ đồng bộ tồn kho thời gian thực và quản lý giá linh hoạt.

## Tính Năng Chính

### Quản Lý Sản Phẩm
- **Thông Tin:**
  - Tên
  - Mã
  - Mô tả
  - Giá
  - Hình ảnh
- **Phân Loại:** Danh mục, danh mục phụ, tags.

### Tồn Kho
- Đồng bộ thời gian thực, tự động trừ khi tạo/xác nhận đơn hàng.
- Hiển thị số âm (màu đỏ) nếu hết hàng.

### Tiered Pricing
- Giá theo nhóm khách hàng, gán thủ công.

## Màn Hình và Tính Năng Chi Tiết

### 1. Danh Sách Products (List View)
- **Hiển Thị:** Danh sách sản phẩm theo danh mục.
- **Tính Năng:**
  - Lọc theo danh mục hoặc tags.
  - Tìm kiếm nhanh theo tên hoặc mã sản phẩm.
  - Xuất danh sách sang Excel.
- **Cải Thiện Giao Diện:**
  - Cố định cột chính (Tên sản phẩm, Mã sản phẩm, Tồn kho) khi cuộn ngang.
  - Tùy chỉnh cột hiển thị (VD: Tên, Giá, Tồn kho).
  - Thanh cuộn thông minh: Thêm nút điều hướng nhanh (trái/phải).
  - Cảnh báo tồn kho: Làm nổi bật sản phẩm có tồn kho ≤ 0 bằng màu đỏ/biểu tượng.

### 2. Chi Tiết Product (Detail View)
- **Hiển Thị:**
  - Thông tin: Mô tả, giá, tồn kho, hình ảnh, danh mục, tags.
- **Tính Năng:**
  - Chỉnh sửa thông tin sản phẩm.
  - Cập nhật tồn kho.
  - Thêm tags.
  - Gán giá theo nhóm khách hàng (Tiered Pricing).
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Product Name            | Tên sản phẩm hoặc dịch vụ.                                               |
| Product Code            | Mã định danh duy nhất, tự động sinh hoặc nhập thủ công.                  |
| Product Category        | Danh mục chính (VD: Điện tử, Phần mềm, Dịch vụ).                         |
| Unit Price              | Giá bán của một đơn vị sản phẩm.                                         |
| Description             | Chi tiết về tính năng, thông số kỹ thuật.                                |
| Product Active          | Active (Đang kinh doanh) hoặc Inactive (Ngừng kinh doanh).               |
| Manufacturer            | Tên nhà sản xuất/nhà cung cấp (liên kết với module Vendors nếu có).      |
| Stock                   | Số lượng hiện có trong kho.                                              |
| Cost Price              | Giá nhập hoặc chi phí sản xuất.                                          |
| Taxable                 | Yes/No hoặc tỷ lệ thuế cụ thể.                                           |
| Tax                     | Loại thuế áp dụng (VD: VAT, Sales Tax) và tỷ lệ.                         |
| Created By              | Tên người tạo sản phẩm.                                                  |
| Created Time            | Thời điểm tạo sản phẩm.                                                  |
| Modified By             | Tên người chỉnh sửa gần nhất.                                            |
| Modified Time           | Thời điểm chỉnh sửa gần nhất.                                            |
| Attachments             | Tài liệu liên quan (VD: tài liệu kỹ thuật).                              |
| Product Image           | Hình ảnh minh họa.                                                       |
| SKU                     | Mã quản lý kho để phân biệt các biến thể.                                |
| Pricing Model           | Fixed Price, Tiered Pricing, Subscription.                               |
| Discount                | Số tiền chiết khấu (nếu có).                                             |
| Unit of Measure         | Đơn vị sản phẩm (VD: Cái, Bộ, Kg).                                       |

- **Cải Thiện Giao Diện:**
  - Chỉnh sửa nhanh: Biểu tượng bút chì khi rê chuột vào trường thông tin.
  - Xem trước hình ảnh: Hiển thị thumbnail, phóng to khi nhấp.
  - Gợi ý danh mục/tags: Tự động gợi ý dựa trên sản phẩm tương tự.

### 3. Tạo Product (Create View)
- **Hiển Thị:** Giao diện nhập thông tin sản phẩm mới.
- **Tính Năng:**
  - Nhập thông tin sản phẩm.
  - Chọn danh mục và tags.
  - Tải lên hình ảnh sản phẩm.
- **Các Trường Thông Tin:** (Tương tự Detail View, nhưng Stock có thể để trống ban đầu).
- **Cải Thiện Giao Diện:**
  - Mẫu sản phẩm: Cung cấp mẫu phổ biến (VD: "Sản phẩm điện tử tiêu chuẩn").
  - Tự động gợi ý mã sản phẩm: Sinh mã dựa trên danh mục hoặc sản phẩm trước.
  - Tải hình ảnh dễ dàng: Hỗ trợ kéo thả và xem trước ngay.

### 4. Quản Lý Tồn Kho (Inventory View)
- **Hiển Thị:** Số lượng tồn kho của từng sản phẩm (màu đỏ nếu ≤ 0).
- **Tính Năng:**
  - Cập nhật tồn kho thủ công.
  - Xem lịch sử thay đổi tồn kho.
- **Cải Thiện Giao Diện:**
  - Cảnh báo tồn kho thấp: Thông báo khi tồn kho ≤ 5 đơn vị.
  - Biểu đồ tồn kho: Hiển thị xu hướng tồn kho dạng biểu đồ.
  - Cập nhật nhanh: Chỉnh sửa số lượng trực tiếp từ danh sách sản phẩm.

---

# Module Deals (Cơ Hội Bán Hàng)

Module Deals quản lý cơ hội bán hàng theo pipeline 5 giai đoạn cố định, ưu tiên B2C, hỗ trợ B2B với dự báo doanh thu và liên kết linh hoạt.

## Tính Năng Chính

### Pipeline
- **Giai Đoạn Cố Định:**
  - New (10%)
  - Contacted (30%)
  - Quoted (60%)
  - Won (100%)
  - Lost (0%)

### Quản Lý
- **Thông Tin:**
  - Tên
  - Giá trị
  - Xác suất (gán thủ công)
  - Khách hàng
  - Người phụ trách
  - Ngày đóng
- **Liên Kết:** Tasks, Notes, Emails.

### Dự Báo Doanh Thu
- **Tính Toán:** Giá trị deal × Xác suất.
- **Hiển Thị:** Trong Pipeline View.

## Màn Hình và Tính Năng Chi Tiết

### 1. Pipeline View
- **Hiển Thị:** Deals theo giai đoạn (New, Contacted, Quoted, Won, Lost) dạng Kanban.
- **Tính Năng:**
  - Kéo thả deal giữa các giai đoạn.
  - Lọc theo người phụ trách.
  - Xem dự báo doanh thu tổng quan.
- **Cải Thiện Giao Diện:**
  - Mã màu giai đoạn (VD: New - xanh, Lost - đỏ).
  - Hiển thị nhanh thông tin: Rê chuột vào deal để xem tên, giá trị, xác suất.
  - Tóm tắt doanh thu: Thêm bảng tổng hợp doanh thu dự kiến ở đầu Pipeline View.

### 2. Danh Sách Deals (List View)
- **Hiển Thị:** Danh sách deals dạng bảng.
- **Tính Năng:**
  - Lọc theo giai đoạn hoặc xác suất.
  - Tìm kiếm nhanh theo tên deal, khách hàng.
  - Xuất dữ liệu sang Excel.
- **Cải Thiện Giao Diện:**
  - Cố định cột chính (Tên deal, Giai đoạn, Giá trị) khi cuộn ngang.
  - Tùy chỉnh cột hiển thị (VD: Tên, Giai đoạn, Xác suất).
  - Thanh cuộn thông minh: Thêm nút điều hướng nhanh (trái/phải).

### 3. Chi Tiết Deal (Detail View)
- **Hiển Thị:**
  - Thông tin: Giá trị, khách hàng, xác suất, sản phẩm/dịch vụ, lịch sử hoạt động.
- **Tính Năng:**
  - Cập nhật xác suất hoặc giai đoạn.
  - Thêm ghi chú.
  - Liên kết với tasks, emails.
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Deal Name               | Tên hoặc tiêu đề (VD: "Hợp đồng với Công ty ABC").                       |
| Account Name            | Khách hàng hoặc công ty (liên kết với module Accounts).                  |
| Contact Name            | Người liên hệ chính (liên kết với module Contacts).                      |
| Amount                  | Giá trị tài chính của giao dịch.                                         |
| Stage                   | Prospecting, Qualification, Negotiation, Closed Won, Closed Lost.        |
| Probability             | Tỷ lệ % khả năng thành công (VD: 70%).                                   |
| Expected Close Date     | Ngày dự kiến hoàn tất.                                                   |
| Assigned To             | Người hoặc nhóm quản lý giao dịch.                                       |
| Lead Source             | Website, Referral, Cold Call, Campaign.                                  |
| Type                    | New Business, Existing Business, Renewal.                                |
| Description             | Chi tiết về yêu cầu khách hàng, hợp đồng.                                |
| Campaign Source         | Chiến dịch liên quan (liên kết với module Campaigns).                    |
| Next Step               | Hành động kế tiếp để thúc đẩy giao dịch.                                 |
| Expected Revenue        | Tính theo Amount × Probability.                                          |
| Created By              | Tên người tạo giao dịch.                                                 |
| Created Time            | Thời điểm tạo giao dịch.                                                 |
| Modified By             | Tên người chỉnh sửa gần nhất.                                            |
| Modified Time           | Thời điểm chỉnh sửa gần nhất.                                            |
| Attachments             | Tài liệu liên quan (VD: hợp đồng, đề xuất).                              |
| Tags                    | Phân loại để tìm kiếm hoặc nhóm giao dịch.                               |
| Activity History        | Nhật ký cập nhật hoặc hành động.                                         |
| Related To              | Liên kết với Quotes, Orders, Products.                                   |
| Products/Services       | Tên sản phẩm, Số lượng, Đơn giá.                                         |
| Notes                   | Ghi chú bổ sung liên quan.                                               |

- **Cải Thiện Giao Diện:**
  - Chỉnh sửa nhanh: Biểu tượng bút chì khi rê chuột vào trường thông tin.
  - Dòng thời gian hoạt động: Hiển thị lịch sử dạng timeline.
  - Gợi ý bước tiếp theo: Đề xuất hành động dựa trên giai đoạn (VD: "Gửi báo giá" cho Quoted).

### 4. Tạo Deal (Create View)
- **Hiển Thị:** Giao diện nhập thông tin deal mới.
- **Tính Năng:**
  - Nhập giá trị, xác suất.
  - Chọn khách hàng, giai đoạn, người phụ trách.
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Deal Name               | Tên hoặc tiêu đề (VD: "Hợp đồng với Công ty ABC").                       |
| Probability             | Tỷ lệ % khả năng thành công (VD: 70%).                                   |
| Expected Close Date     | Ngày dự kiến hoàn tất.                                                   |
| Assigned To             | Người hoặc nhóm quản lý.                                                 |
| Description             | Chi tiết về yêu cầu khách hàng, hợp đồng.                                |

- **Cải Thiện Giao Diện:**
  - Mẫu deal: Cung cấp mẫu phổ biến (VD: "Hợp đồng B2C tiêu chuẩn").
  - Gợi ý xác suất: Tự động gợi ý dựa trên giai đoạn (VD: 10% cho New).
  - Xem trước liên kết: Hiển thị thông tin cơ bản của Account/Contact khi chọn.

---

# Module Accounts (Khách Hàng)

Module Accounts quản lý thông tin khách hàng B2C (lặp lại) và B2B, lưu trữ lịch sử đầy đủ và hỗ trợ marketing cá nhân hóa.

## Tính Năng Chính

### Quản Lý Thông Tin
- **Thông Tin:**
  - Tên
  - Email
  - Điện thoại
  - Địa chỉ
  - Ngày sinh (tùy chọn)
  - Sở thích (tùy chọn)
  - Công ty (tùy chọn)
  - Người liên hệ (tùy chọn)
- **Phân Loại:** Quy mô, ngành, khu vực (tùy chọn).

### Lịch Sử
- **Giao Dịch:** Đơn hàng (Orders).
- **Cơ Hội Bán Hàng:** Deals.
- **Hoạt Động:** Ghi chú, email, cuộc gọi.

### Liên Kết
- Với Leads, Deals, Orders.

### Lọc
- Lọc theo ngày sinh (VD: sinh nhật trong tháng) để hỗ trợ chiến dịch B2C.
- Lọc theo ngành, khu vực hoặc quy mô.

## Màn Hình và Tính Năng Chi Tiết

### 1. Danh Sách Accounts (List View)
- **Hiển Thị:** Danh sách khách hàng dạng bảng.
- **Tính Năng:**
  - Lọc theo ngành, khu vực hoặc ngày sinh.
  - Tìm kiếm nhanh theo tên, email hoặc công ty.
  - Xuất danh sách sang Excel.
- **Cải Thiện Giao Diện:**
  - Cố định cột chính (Tên, Email, Công ty) khi cuộn ngang.
  - Tùy chỉnh cột hiển thị (VD: Tên, Email, Ngày sinh).
  - Thanh cuộn thông minh: Thêm nút điều hướng nhanh (trái/phải).
  - Biểu tượng trực quan: Dùng màu sắc/biểu tượng để phân biệt khách B2C/B2B.

### 2. Chi Tiết Account (Detail View)
- **Hiển Thị:**
  - Thông tin: Liên hệ, ngày sinh, sở thích, công ty, quy mô, ngành, khu vực.
  - Lịch sử: Đơn hàng, deals, hoạt động (ghi chú, email, cuộc gọi).
  - Thông tin từ Leads View (giữ nguyên dữ liệu liên quan từ Leads).
- **Tính Năng:**
  - Chỉnh sửa thông tin khách hàng.
  - Thêm người liên hệ.
  - Liên kết với deal hoặc order.
  - Thêm ghi chú.
- **Các Trường Thông Tin:**

| **Trường**              | **Mô Tả**                                                                 |
|--------------------------|--------------------------------------------------------------------------|
| Account Name            | Tên cá nhân (B2C) hoặc công ty (B2B).                                    |
| Email                   | Địa chỉ email chính.                                                     |
| Phone                   | Số điện thoại liên hệ.                                                   |
| Address                 | Địa chỉ khách hàng (tùy chọn).                                           |
| Birthday                | Ngày sinh khách hàng (tùy chọn, hỗ trợ chiến dịch B2C).                  |
| Interests               | Thông tin sở thích (tùy chọn, hỗ trợ marketing cá nhân hóa).             |
| Company                 | Tên công ty (tùy chọn, chủ yếu cho B2B).                                 |
| Contact Name            | Người liên hệ chính (tùy chọn, liên kết với module Contacts).            |
| Scale                   | Quy mô khách hàng (VD: Cá nhân, Doanh nghiệp nhỏ, Doanh nghiệp lớn).     |
| Industry                | Ngành nghề (VD: Công nghệ, Bán lẻ).                                      |
| Region                  | Khu vực địa lý (VD: Hà Nội, TP.HCM).                                     |
| Created By              | Tên người tạo account.                                                   |
| Created Time            | Thời điểm tạo account.                                                   |
| Modified By             | Tên người chỉnh sửa gần nhất.                                            |
| Modified Time           | Thời điểm chỉnh sửa gần nhất.                                            |
| Related To              | Liên kết với Leads, Deals, Orders.                                       |
| Activity History        | Nhật ký đơn hàng, deals, ghi chú, email, cuộc gọi.                       |

- **Cải Thiện Giao Diện:**
  - Chỉnh sửa nhanh: Biểu tượng bút chì khi rê chuột vào trường thông tin.
  - Dòng thời gian hoạt động: Hiển thị lịch sử dạng timeline.
  - Gợi ý chiến dịch: Đề xuất chiến dịch marketing dựa trên ngày sinh/sở thích.

### 3. Tạo Account (Create View)
- **Hiển Thị:** Giao diện nhập thông tin khách hàng mới.
- **Tính Năng:**
  - Nhập thông tin: Tên, email, điện thoại, ngày sinh, sở thích.
  - Chọn ngành, quy mô, khu vực.
  - Thêm người liên hệ.
- **Các Trường Thông Tin:** (Tương tự Detail View, nhưng không cần lịch sử).
- **Cải Thiện Giao Diện:**
  - Mẫu khách hàng: Cung cấp mẫu (VD: "Khách hàng B2C tiêu chuẩn").
  - Gợi ý thông tin: Tự động điền email/điện thoại từ Leads nếu liên kết.
  - Kiểm tra trùng lặp: Cảnh báo nếu tên, email hoặc điện thoại đã tồn tại.

## Quy Tắc Tạo Account
- **Vấn Đề:** Có nên yêu cầu chuyển từ Lead hay cho phép tạo thẳng Account?
- **Đề Xuất Giải Pháp:**
  - **Cho phép tạo thẳng:** Hỗ trợ nhập trực tiếp cho khách B2C/B2B đã xác định (VD: khách quay lại, đối tác B2B mới). Thêm tùy chọn "Chuyển từ Lead" để giữ thông tin từ Leads View.
  - **Cảnh báo khi tạo mới:** Thông báo nếu thông tin (email, điện thoại) trùng với Lead hiện có, gợi ý liên kết thay vì tạo mới.
- **Quy Trình Đề Xuất:**
  - Mặc định: Tạo Account trực tiếp với đầy đủ thông tin.
  - Tùy chọn: Liên kết với Lead để tự động điền thông tin từ Leads View.
  - Lưu trữ: Giữ thông tin từ Leads View trong Activity History của Account.

